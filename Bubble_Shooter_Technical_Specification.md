# Title Format: Bubble Shooter Game - Frontend Technical Solution

# Project Information

| **PRD** | [Bubble Shooter Game Requirements](./PinataParty_Technical_Documentation.md) |
| **Jira Epic** | _TBD_ |
| **project page link (optional)** | _TBD_ |
| **stage** | DRAFT |
| **Document Owner (Dev)** | _TBD_ |
| **Document Owner (QA)** | _TBD_ |

# Revision History

| **Revision** | **Date** | **Modified** | **amendment** |
| v0.1 | 2025.7.31 | Initial Draft | Created the basic structure and core technical design |

# 1. Background and Purpose

## 1.1 Requirements Background

- Based on the successful PinataParty pinball game implementation
- Need to create a bubble shooter game with similar physics-based mechanics
- Requirement for high-performance physics engine with collision detection
- Support for both Web Worker and built-in physics engine modes

## 1.2 Requirement Purpose

- Provide engaging bubble shooter gameplay experience
- Implement robust physics-based bubble collision and popping mechanics
- Support multiplayer/social features similar to Cake Party event structure
- Ensure smooth performance across different platforms and devices

## 1.3 UX Flow

The game follows a similar structure to Cake Party with team formation and cooperative gameplay elements, combined with the physics-based mechanics from PinataParty.

# 2. Terminology

| Name | Meaning | Description |
| Bubble Shooter | A puzzle game where players shoot colored bubbles to match and pop groups | Core gameplay mechanic |
| Physics Engine | System handling collision detection and bubble movement | Based on PinataParty's dual-mode physics system |
| Web Worker | Separate thread for physics calculations | Performance optimization technique |
| Collision Matrix | Configuration defining which objects can collide | Physics system optimization |
| Team Formation | Social feature allowing players to form cooperative teams | Similar to Cake Party mechanics |

# 3. Architecture Design

## 3.1 Existing Architecture Reference

Based on PinataParty's proven architecture:

| Function Name | Description |
| BubbleShooterScene | Main scene entry point, similar to PinataPartyScene |
| BubbleShooterController | Core gameplay controller managing bubble shooting mechanics |
| BubblePhysicsManager | Physics engine management (Web Worker or built-in) |
| BubbleCollisionSystem | Collision detection and bubble popping logic |
| TeamFormationManager | Social features for team-based gameplay |
| ScoreManager | Scoring system with multipliers and bonuses |

## 3.2 Shortcoming of Existing Structure

- PinataParty's physics system is optimized for single ball mechanics
- Need to adapt for multiple bubble objects simultaneously
- Require enhanced collision detection for bubble matching logic
- Need social features integration for team-based gameplay

## 3.3 Logical Architecture Design

```
BubbleShooterScene (Scene Entry)
├── BubbleShooterController (Core Controller)
├── BubbleShooterManager (Game Management)
├── BubbleShooterState (State Management)
├── PhysicsWorker (Web Worker Physics) / PhysicsSystem2D (Built-in Physics)
├── TeamFormationManager (Social Features)
└── BubbleMatchingEngine (Bubble Matching Logic)
```

### Core Components:

#### 3.3.1 Controller Layer
- **BubbleShooterGameplayController**: Core shooting and physics control
- **BubbleShooterAreaController**: UI and game area management
- **BubbleController**: Individual bubble behavior and properties
- **BubbleCollisionController**: Collision detection and response
- **TeamFormationController**: Social team management

#### 3.3.2 Manager Layer
- **BubbleShooterGameplayManager**: Game flow and state management
- **BubbleShooterLPManager**: Landing page and UI management
- **BubbleMatchingManager**: Bubble matching and popping logic
- **BubblePhysicsManager**: Physics engine coordination
- **TeamProgressManager**: Team-based progress tracking

#### 3.3.3 State Management
- **BubbleShooterState**: Game state data structure
- **TeamState**: Team formation and progress state
- **PhysicsState**: Physics engine state management

## 3.4 Technical Selection

### Core Framework
- **Cocos Creator**: Main game engine (consistent with existing games)
- **TypeScript**: Primary development language
- **Web Worker API**: For high-performance physics calculations
- **PhysicsSystem2D**: Built-in physics engine fallback

### Physics Engine Selection
Following PinataParty's dual-mode approach:
```typescript
export function usePhysicsWorker(): boolean {
  const queryParam = queryParamSelector(store.getState());
  return IS_RUNTIME || queryParam.force_worker;
}
```

**Web Worker Mode (Production)**:
- ✅ Independent physics thread
- ✅ Non-blocking main thread
- ✅ Better performance for complex scenes
- ✅ Automatic activation in runtime environment

**Built-in Physics Mode (Development)**:
- ✅ Easier debugging
- ✅ Direct event handling
- ✅ Simpler development workflow
- ✅ Manual activation via URL parameter

# 4. Detailed Design

## 4.1 Timing Diagrams and Flowcharts

### 4.1.1 Game Initialization Flow

```
Game Start → Initialize Physics Engine → Load Bubble Grid → 
Setup Team Formation → Enable Player Input → Ready to Shoot
```

### 4.1.2 Bubble Shooting Flow

```
Player Aim → Calculate Trajectory → Shoot Bubble → 
Physics Collision → Match Detection → Bubble Pop → 
Score Calculation → Team Progress Update
```

### 4.1.3 Team Formation Flow (Based on Cake Party)

```
Player Entry → Display Team Invitation → Send/Receive Invitations → 
Form Team → Cooperative Gameplay → Shared Progress → Team Rewards
```

## 4.2 Core Component Design

### 4.2.1 Physics Engine Integration

**Web Worker Physics Mode**:
```typescript
// BubblePhysicsWorkerController.ts
export class BubblePhysicsWorkerController extends PinataPartyWebWorkerController {
  private bubbleBodyMap: Map<string, BubbleDefinition> = new Map();
  
  public shootBubble(bubble: BubbleController, velocity: Vec2) {
    const bodyDefinition = this.getBubbleBodyDefinition(bubble);
    bodyDefinition.linearVelocity = velocity;
    this.updateBody([bodyDefinition]);
  }
  
  public handleBubbleCollision(message: WorkerCollisionMessage) {
    // Process bubble-to-bubble collisions
    // Trigger matching logic
    // Handle bubble popping
  }
}
```

**Built-in Physics Mode**:
```typescript
// BubblePhysicsController.ts
export class BubblePhysicsController {
  public async shootBubble(bubble: BubbleController, velocity: Vec2) {
    bubble.rigidBody.enabled = false;
    PhysicsSystem2D.instance.enable = true;
    
    applyColliderRecursively(this.gameArea);
    await AnimationHelper.Delay(0.01); // Physics stabilization
    
    bubble.rigidBody.enabled = true;
    bubble.rigidBody.linearVelocity = velocity;
  }
}
```

### 4.2.2 Bubble Matching Engine

```typescript
// BubbleMatchingEngine.ts
export class BubbleMatchingEngine {
  public checkMatches(newBubble: BubbleController): BubbleMatch[] {
    const matches: BubbleMatch[] = [];
    const visited = new Set<string>();
    
    // Flood fill algorithm for matching bubbles
    const connectedBubbles = this.floodFill(newBubble, visited);
    
    if (connectedBubbles.length >= MINIMUM_MATCH_COUNT) {
      matches.push({
        bubbles: connectedBubbles,
        score: this.calculateMatchScore(connectedBubbles),
        type: this.getMatchType(connectedBubbles)
      });
    }
    
    return matches;
  }
  
  private floodFill(bubble: BubbleController, visited: Set<string>): BubbleController[] {
    // Implement flood fill algorithm for connected bubbles of same color
  }
}
```

### 4.2.3 Team Formation System (Adapted from Cake Party)

```typescript
// TeamFormationManager.ts
export class TeamFormationManager {
  public async sendTeamInvitation(friendId: string, teamSlot: number) {
    // Send invitation similar to Cake Party
    const invitation: TeamInvitation = {
      inviterId: this.currentPlayerId,
      inviteeId: friendId,
      teamSlot: teamSlot,
      timestamp: Date.now()
    };
    
    await this.services.teamFormation.sendInvitation(invitation);
  }
  
  public async acceptInvitation(invitation: TeamInvitation) {
    // Accept invitation and form team
    if (this.canFormTeam(invitation.teamSlot)) {
      await this.services.teamFormation.acceptInvitation(invitation);
      this.updateTeamState(invitation);
    }
  }
}
```

## 4.3 Collision System Design

### 4.3.1 Collision Types

```typescript
export enum BUBBLE_SHOOTER_COLLIDER {
  WALL = 0,           // Game boundaries
  BUBBLE = 1,         // Shootable bubbles
  GRID_BUBBLE = 2,    // Fixed grid bubbles
  CEILING = 3,        // Top boundary for attachment
  SENSOR = 4,         // Scoring sensors
  OBSTACLE = 5,       // Game obstacles
}
```

### 4.3.2 Bubble Attachment Logic

```typescript
// BubbleAttachmentController.ts
export class BubbleAttachmentController {
  public handleBubbleCollision(shootingBubble: BubbleController, targetBubble: BubbleController) {
    // Calculate attachment position
    const attachmentPosition = this.calculateAttachmentPosition(shootingBubble, targetBubble);

    // Attach bubble to grid
    this.attachBubbleToGrid(shootingBubble, attachmentPosition);

    // Check for matches
    const matches = this.bubbleMatchingEngine.checkMatches(shootingBubble);

    if (matches.length > 0) {
      this.processBubbleMatches(matches);
    }
  }
}
```

## 4.4 State Management Design

### 4.4.1 Game State Structure

```typescript
export interface BubbleShooterState {
  // Game Progress
  currentLevel: number;
  score: number;
  shotsRemaining: number;

  // Physics State
  isPhysicsActive: boolean;
  useWebWorker: boolean;

  // Bubble Grid State
  gridBubbles: BubbleGridState[][];
  activeBubble: BubbleController | null;

  // Team State (from Cake Party pattern)
  teamId: string | null;
  teamMembers: TeamMember[];
  teamProgress: TeamProgress;

  // UI State
  isGameplayActive: boolean;
  showTeamInvitation: boolean;
  currentAnimation: string | null;
}
```

### 4.4.2 Team State Management (Cake Party Pattern)

```typescript
export interface TeamState {
  teams: {
    team1: TeamInfo | null;
    team2: TeamInfo | null;
    team3: TeamInfo | null;
  };
  invitations: TeamInvitation[];
  teamProgress: {
    [teamId: string]: {
      currentScore: number;
      targetScore: number;
      completed: boolean;
      rewards: RewardItem[];
    };
  };
}
```

## 4.5 Scoring System Design

### 4.5.1 Base Scoring Mechanics

```typescript
// BubbleScoreManager.ts
export class BubbleScoreManager {
  private baseScores = {
    BASIC_MATCH: 10,      // 3 bubbles
    COMBO_MATCH: 25,      // 4+ bubbles
    CHAIN_BONUS: 50,      // Chain reactions
    PRECISION_BONUS: 100, // Bank shots
    CLEAR_BONUS: 500,     // Level clear
  };

  public calculateMatchScore(match: BubbleMatch): number {
    let score = 0;

    // Base score for bubble count
    if (match.bubbles.length >= 3) {
      score += this.baseScores.BASIC_MATCH * match.bubbles.length;
    }

    // Combo bonus for large matches
    if (match.bubbles.length >= 4) {
      score += this.baseScores.COMBO_MATCH * (match.bubbles.length - 3);
    }

    return score;
  }
}
```

## 4.6 Security Solution Design

### 4.6.1 Anti-Cheat Measures

```typescript
// BubbleShooterSecurity.ts
export class BubbleShooterSecurityManager {
  public validateShot(shotData: ShotData): boolean {
    // Validate shot trajectory
    if (!this.validateTrajectory(shotData.startPosition, shotData.endPosition, shotData.velocity)) {
      return false;
    }

    // Validate score calculation
    if (!this.validateScoreCalculation(shotData.score, shotData.matchData)) {
      return false;
    }

    return true;
  }

  public encryptGameData(gameData: GameSubmissionData): Promise<string> {
    // Encrypt sensitive game data before submission
    return this.services.bubbleShooter.getEncryptedData(gameData);
  }
}
```

## 4.7 Exception Handling Scheme Design

### 4.7.1 Physics Engine Error Handling

```typescript
// BubblePhysicsErrorHandler.ts
export class BubblePhysicsErrorHandler {
  public handleWorkerError(error: WorkerError) {
    console.error('Physics Worker Error:', error);

    // Fallback to built-in physics
    this.fallbackToBuiltinPhysics();

    // Log error for monitoring
    this.logPhysicsError(error);
  }

  private fallbackToBuiltinPhysics() {
    // Disable Web Worker and enable built-in physics
    PhysicsSystem2D.instance.enable = true;
    this.bubbleShooterState.useWebWorker = false;
  }
}
```

### 4.7.2 Network Error Handling

```typescript
// NetworkErrorHandler.ts
export class NetworkErrorHandler {
  public async handleSubmissionError(error: NetworkError, gameData: GameSubmissionData) {
    // Store failed submission for retry
    this.storeFailedSubmission(gameData);

    // Show user-friendly error message
    this.showNetworkErrorDialog();

    // Attempt retry with exponential backoff
    await this.retrySubmission(gameData);
  }
}

## 4.8 Platform Compatibility Scheme Design

### 4.8.1 Multi-Platform Support

| Platform | Physics Engine | Performance Optimization | Special Considerations |
|----------|----------------|--------------------------|------------------------|
| **Mobile (iOS/Android)** | Web Worker preferred | Reduced particle effects | Touch input optimization |
| **Desktop Browser** | Built-in Physics for debug | Full visual effects | Mouse input support |
| **WeChat Mini Program** | Web Worker required | Compressed assets | WeChat API integration |

### 4.8.2 Performance Optimization

```typescript
// PlatformOptimizer.ts
export class PlatformOptimizer {
  public optimizeForPlatform() {
    const platform = this.detectPlatform();

    switch (platform) {
      case Platform.MOBILE:
        this.enableMobileOptimizations();
        break;
      case Platform.DESKTOP:
        this.enableDesktopFeatures();
        break;
      case Platform.WECHAT:
        this.enableWeChatOptimizations();
        break;
    }
  }

  private enableMobileOptimizations() {
    // Reduce physics simulation quality for better performance
    this.physicsQuality = PhysicsQuality.MEDIUM;
    this.particleCount = 50; // Reduced particle count
    this.enableObjectPooling = true;
  }
}
```

# 5. The Caveats of Architecture

## 5.1 Known Limitations

- **Physics Engine Switching**: Runtime switching between Web Worker and built-in physics requires game restart
- **Team Formation Complexity**: Managing team state across multiple players requires robust synchronization
- **Memory Management**: Large bubble grids may cause memory pressure on low-end devices
- **Network Dependency**: Team features require stable network connection

## 5.2 Performance Trade-offs

- **Web Worker Mode**: Higher memory usage but better performance
- **Built-in Physics Mode**: Lower memory usage but potential frame drops
- **Team Synchronization**: Real-time updates vs. battery life optimization

# 6. Interface Design

## 6.1 Game Data Interfaces

```typescript
// Core game interfaces
export interface BubbleData {
  id: string;
  color: BubbleColor;
  position: Vec3;
  isAttached: boolean;
  gridPosition?: GridPosition;
}

export interface GameSubmissionData {
  request_id: string;
  enc: string;
  level_id: number;
  score: number;
  shots_used: number;
  elapsed_time: number;
  bubbles_popped: number;
  team_id?: string;
}

export interface TeamInvitation {
  inviterId: string;
  inviteeId: string;
  teamSlot: number;
  timestamp: number;
  status: InvitationStatus;
}
```

## 6.2 API Endpoints

```typescript
// Service interfaces
export interface BubbleShooterService {
  // Game submission
  submitGameResult(data: GameSubmissionData): Promise<GameSubmissionResponse>;

  // Team management
  sendTeamInvitation(invitation: TeamInvitation): Promise<boolean>;
  acceptTeamInvitation(invitationId: string): Promise<TeamInfo>;
  getTeamProgress(teamId: string): Promise<TeamProgress>;

  // Level management
  getLevelData(levelId: number): Promise<LevelData>;
  unlockLevel(levelId: number): Promise<boolean>;
}
```

# 7. Front-end Monitoring

## 7.1 Performance Metrics

- **Physics Engine Performance**
  - Physics simulation FPS
  - Collision detection latency
  - Memory usage tracking

- **Game Performance**
  - Frame rate stability
  - Loading time metrics
  - Crash rate monitoring

## 7.2 User Behavior Analytics

- **Gameplay Metrics**
  - Level completion rates
  - Average shots per level
  - Team formation success rate

- **Engagement Metrics**
  - Session duration
  - Daily active users
  - Team participation rate

# 8. External Dependencies and Restrictions

## 8.1 Internal Dependencies

- **Cocos Creator Engine**: v3.x required for physics system compatibility
- **Game Services**: Integration with existing game backend services
- **Team System**: Dependency on social features infrastructure
- **Analytics System**: Integration with existing analytics pipeline

## 8.2 External Restrictions

- **Platform Limitations**: iOS/Android WebView performance constraints
- **Network Requirements**: Stable connection required for team features
- **Memory Constraints**: Mobile device memory limitations
- **Physics Engine**: Web Worker support varies by platform

# 9. Remaining Problems and Risk Estimation

## 9.1 Technical Risks

1. **Physics Engine Compatibility**: Web Worker physics may not work on all target platforms
2. **Team Synchronization**: Real-time team progress updates may cause performance issues
3. **Memory Management**: Large bubble grids may exceed mobile device memory limits
4. **Network Reliability**: Team features depend on stable network connectivity

## 9.2 Mitigation Strategies

1. **Fallback Systems**: Built-in physics engine as fallback for Web Worker issues
2. **Offline Mode**: Local gameplay when network is unavailable
3. **Progressive Loading**: Load bubble grids in chunks to manage memory
4. **Error Recovery**: Robust error handling and state recovery mechanisms

# 10. Publish Plan

## 10.1 Release Strategy

### Phase 1: Core Gameplay (MVP)
- Basic bubble shooting mechanics
- Physics engine integration
- Single-player mode
- Level progression system

### Phase 2: Social Features
- Team formation system
- Cooperative gameplay
- Team progress tracking
- Social rewards

### Phase 3: Advanced Features
- Advanced physics effects
- Enhanced animations
- Performance optimizations
- Platform-specific features

## 10.2 Rollback Solution

- **Configuration Rollback**: Ability to disable new features via remote config
- **Version Rollback**: Maintain previous stable version for emergency rollback
- **Data Migration**: Ensure backward compatibility for save data
- **Team State Recovery**: Robust team state recovery mechanisms

# 11. Appendix

## 11.1 Configuration Keys

| Scene | Type | Admin Key | Description |
|-------|------|-----------|-------------|
| Bubble Shooter Home | label | text_bubble_shooter_title | Main game title |
| Team Formation | label | text_team_invitation_title | Team invitation modal title |
| Gameplay | label | text_shots_remaining | Shots remaining counter |
| Score Display | label | text_current_score | Current score display |
| Team Progress | label | text_team_progress | Team progress indicator |

## 11.2 Asset Requirements

| Asset Type | Format | Usage | Notes |
|------------|--------|-------|-------|
| Bubble Sprites | PNG | Bubble colors and states | 6 colors + special bubbles |
| Physics Shapes | JSON | Collision boundaries | Generated from sprites |
| UI Elements | PNG | Game interface | Consistent with game style |
| Animations | DragonBones | Bubble pop effects | Optimized for mobile |
| Audio | MP3/OGG | Sound effects and music | Compressed for web |
```
