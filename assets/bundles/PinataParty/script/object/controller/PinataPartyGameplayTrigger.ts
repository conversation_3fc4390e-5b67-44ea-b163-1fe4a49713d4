import { _decorator, Component, Node, EventTouch, Vec3, UITransform } from 'cc';
import AnimationHelper from '../../../../../script/lib/util/animationHelper';
import { GameInstance } from '../../../../../script/object/GameInstance';
import {
  MAXIMUM_SPRING_DELTA,
  MINIMUM_SPRING_DELTA,
  VELOCITY_MULTIPLIER,
  VELOCITY_ADDITION,
  MAXIMUM_RANDOM_FLOAT,
} from '../constants/PinataPartyContstants';
import { randomBetweenFloat } from '../../../../../script/util/random';
import { AUDIO_KEY } from '../../../../../script/lib/enum/audio';
const { ccclass, property } = _decorator;

@ccclass('PinataPartyGameplayTrigger')
export class PinataPartyGameplayTrigger extends Component {
  private initialPosition?: Vec3 = Vec3.ZERO;

  private initialSpringHeight: number = 0;

  @property(UITransform)
  private spring?: UITransform;

  @property(Node)
  private grip?: Node;

  start() {
    this.registerTouchEvent();
    this.initialSpringHeight =
      this.spring?.getComponent(UITransform)?.height || 0;
  }

  private registerTouchEvent() {
    if (!this.grip) return;
    this.grip.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
    this.grip.on(Node.EventType.TOUCH_MOVE, this.moveSpringEvent, this);
    this.grip.on(Node.EventType.TOUCH_END, this.letGoSpringEvent, this);
    this.grip.on(Node.EventType.TOUCH_CANCEL, this.letGoSpringEvent, this);
  }

  private onTouchStart() {
    GameInstance.pinataPartyManager.pinataPartyGameplayManager.resetHandAnimation();
    GameInstance.pinataPartyManager.pinataPartyGameplayManager.checkCanPlay();
    this.initialPosition = this.grip?.getWorldPosition() || Vec3.ZERO;

    if (GameInstance.pinataPartyState.canPlay) {
      GameInstance.generalController.soundEffectController.play(
        AUDIO_KEY.PINATA_PARTY_PULL_TRIGGER_SOUND,
        {
          isLoop: true,
        },
      );
    } else if (GameInstance.pinataPartyState.currentPearl === 0) {
      GameInstance.pinataPartyController.pinataPartyAreaController.showLevelGoalPopup();
    }
  }

  private moveSpringEvent(event: EventTouch) {
    GameInstance.pinataPartyManager.pinataPartyGameplayManager.resetHandAnimation();
    if (
      GameInstance.pinataPartyState.isPlaying ||
      !GameInstance.pinataPartyState.canPlay ||
      !this.initialPosition
    )
      return;
    const delta = event.getLocationY() - this.initialPosition.y;
    if (delta > MAXIMUM_SPRING_DELTA) return;
    if (delta < MINIMUM_SPRING_DELTA) return;
    if (!this.spring || !this.grip) return;
    this.grip.worldPosition = new Vec3(
      this.node.getWorldPosition().x,
      event.getLocationY(),
      1,
    );
    this.spring.height = this.initialSpringHeight + delta;
  }

  private async letGoSpringEvent(event: EventTouch) {
    GameInstance.generalController.soundEffectController.stop(
      AUDIO_KEY.PINATA_PARTY_PULL_TRIGGER_SOUND,
    );

    if (
      GameInstance.pinataPartyState.isPlaying ||
      !GameInstance.pinataPartyState.canPlay
    )
      return;
    if (!this.spring || !this.grip || !this.initialPosition) return;

    // if spring has not changed, indicating user hasn't moved the grip yet, let go event won't be triggered
    if (this.spring.height === this.initialSpringHeight) return;

    GameInstance.generalController.soundEffectController.play(
      AUDIO_KEY.PINATA_PARTY_LAUNCH_TRIGGER_SOUND,
    );

    const delta = Math.max(
      Math.min(
        event.getLocationY() - this.initialPosition.y,
        MAXIMUM_SPRING_DELTA,
      ),
      MINIMUM_SPRING_DELTA,
    );
    AnimationHelper.MoveToTargetWorldPosition(
      this.grip,
      0.02,
      this.initialPosition,
    );
    await AnimationHelper.ResizeUITransform(this.spring, 0.02, {
      height: this.initialSpringHeight,
    });
    this.grip.worldPosition = this.initialPosition;
    this.spring.height = this.initialSpringHeight;

    let velocity =
      -delta * VELOCITY_MULTIPLIER +
      VELOCITY_ADDITION +
      randomBetweenFloat(0, MAXIMUM_RANDOM_FLOAT);

    velocity = parseFloat(velocity.toFixed(3));

    GameInstance.pinataPartyManager.pinataPartyGameplayManager.startPlay(
      velocity,
    );
  }
}
