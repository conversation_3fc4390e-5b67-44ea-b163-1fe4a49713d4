import {
  PinataPartyController,
  PinataPartyState,
  PinataShootLog,
  PinataPartySubmitData,
} from '../interface/PinataPartyInterface';
import { Position } from '../../../../../script/interface';
import { logData } from '../../../../../script/util/log';
import { PinataPartyScoreManager } from './PinataPartyScoreManager';
import { GameplayService, SavedData } from '../../../../../script/interface';
import { PINATA_SCORE_MULTIPLIER } from '../../constants';
import { PinataPartyAdsButtonConfig } from '../../interface';
import { ADS_SCENARIO } from '../../../../../script/enum/ads';
import { LoadingManager } from '../../../../../script/object/manager/LoadingManager';
import {
  clearPinataPartyPendingSubmits,
  savePinataPartyPendingSubmits,
  submitPinataPartyGameplay,
} from '../../../../../script/util/pinataPartySubmitUtil';
import { SceneManager } from '../../../../../script/object/manager/SceneManager';
import { SCENE_KEY } from '../../../../../script/enum/scene';
import { ServerError } from '../../../../../script/util/error';
import {
  PinataPartyGameplayTrackerConfig,
  PinataPartySubmitResponse,
} from '../../../../../script/interface/pinataParty';
import { getUserId } from '../../../../../script/util/getUserId';
import {
  APP_VERSION,
  BASE_SIZE_REWARD_ASSET,
} from '../../../../../script/constants';
import serverTimestampTicker from '../../../../../script/util/serverTimestampTicker';
import { MAX_PLAY_TIME } from '../../../../../script/constants/levels';
import {
  clearGameData,
  saveGameData,
} from '../../../../../script/lib/util/autosave';
import {
  AUTOSAVE_TYPE,
  GIFT_POPUP_TYPE,
  EventScheduleType,
} from '../../../../../script/enum';
import { GeneralState } from '../../../../../script/object/state/GeneralState';
import { generateRequestId } from '../../../../../script/util/levelUtil';
import { isOnMobileApp } from '../../../../../script/util/platformChecker';
import {
  MINIMUM_VELOCITY,
  VELOCITY_ADDITION,
} from '../constants/PinataPartyContstants';
import AnimationHelper from '../../../../../script/lib/util/animationHelper';
import {
  getPinataPointsProgressBarConfig,
  getPinataScoreAnimationConfig,
} from '../util/config';
import { getPinataGrandPrizeConfig } from '../../../../../script/util/reward';
import {
  CreateTextConfig,
  EventRewardItem,
  GrandPrizePopUpConfig,
} from '../../../../../script/interface/UI';
import { getPopUpContainerConfig } from '../../../../../script/config/PopUpConfig';
import { colorConfigSelector } from '../../../../../redux/selector/general';
import { store } from '../../../../../redux/stores';
import { AUDIO_KEY } from '../../../../../script/lib/enum/audio';
import { SoundEffectController } from '../../../../../script/object/controller/SoundEffectController';
import { PopUpMasterController } from '../../../../../script/object/controller/PopUps/PopUpMasterController';
import { WEBWORKER_COMMAND } from '../../worker/workerEnum';
import { transformTextBE } from '../../../../../script/util/encryptUtil';

const PENDING_SUBMIT_LIMIT = 2;
const MAX_BOUNCE_HEIGHT = 220;
const CHEST_POSITION_OFFSET_X = 0;
const CHEST_POSITION_OFFSET_Y = 0;

export class PinataPartyGameplayManager {
  constructor(
    private services: GameplayService,
    private generalState: GeneralState,
    private soundEffectController: SoundEffectController,
    private sceneManager: SceneManager,
    private loadingManager: LoadingManager,
    private pinataPartyState: PinataPartyState,
    private pinataPartyController: PinataPartyController,
    private scoreManager: PinataPartyScoreManager,
    private popUpMasterController: PopUpMasterController,
  ) {}

  public shootingLog: PinataShootLog[] = [];

  public isDebugPlay: boolean = false;
  public currentShootLog: PinataShootLog | null = null;

  public async initGameplay() {
    this.initAdsButton();
    this.pinataPartyController.pinataPartyGameplayController.initScoreGameplay();

    // set initial multiplier (1x)
    this.setMultiplier(0);

    if (this.generalState.savedData?.type === AUTOSAVE_TYPE.PINATA) {
      await this.pinataPartyController.pinataPartyWebWorkerController?.waitForReady();
      await this.pinataPartyController.pinataPartyAreaController.openGameplaySection();
      this.setMultiplier(this.generalState.savedData.multiplierIndex);
      this.startPlay(this.generalState.savedData.velocity);
      this.generalState.savedData = null;
    }
  }

  /**
   * Change to next score multiplie
   */
  public changeMultiplier() {
    if (this.pinataPartyState.isPlaying) return;
    this.setMultiplier(this.pinataPartyState.multiplierIndex + 1);
  }

  /**
   * Make sure user has enough pearl for multiplier
   */
  public checkMultiplier() {
    this.setMultiplier(this.pinataPartyState.multiplierIndex);
  }

  /**
   * set active score multiplier
   * @param multiplierIndex index of the index multiplier to be activated
   */
  private setMultiplier(multiplierIndex: number) {
    let index = multiplierIndex;
    if (index >= PINATA_SCORE_MULTIPLIER.length) index = 0;

    // Player doesn't have enough pearl
    if (this.pinataPartyState.currentPearl < PINATA_SCORE_MULTIPLIER[index]) {
      index = 0;
    }

    this.pinataPartyState.multiplier = PINATA_SCORE_MULTIPLIER[index];
    this.pinataPartyState.multiplierIndex = index;
    this.pinataPartyState.rubberScore = PINATA_SCORE_MULTIPLIER[index];

    this.pinataPartyController.pinataScoreMultiplierButtonController.refreshLabel();
    this.pinataPartyController.pinataPartyGameplayController.refreshPoint();
  }

  /**
   * Check if user can shoot balll
   * Should be called before every startPlay()
   */
  public async checkCanPlay() {
    const reachPendingSubmitLimit =
      this.pinataPartyState.pendingSubmits.length >= PENDING_SUBMIT_LIMIT;

    // just to be safe, check multiplier 1st
    this.checkMultiplier();
    const enoughPearl = this.pinataPartyState.currentPearl > 0;

    this.pinataPartyState.canPlay = !reachPendingSubmitLimit && enoughPearl;

    if (reachPendingSubmitLimit) {
      await this.submitAllPendingSubmit(true);
    }
  }

  public async startPlayDebug(
    start: number,
    end: number | undefined,
    delta: number | undefined,
  ) {
    const startVelocity = Math.max(MINIMUM_VELOCITY, start);
    const endVelocity = end && end > startVelocity ? end : startVelocity;
    const deltaVelocity = delta || 1;
    this.isDebugPlay = true;

    for (
      let force = startVelocity;
      force <= endVelocity;
      force += deltaVelocity
    ) {
      const shootLog = await this.startPlay(force, false, true);
      this.shootingLog.push(shootLog);
      this.currentShootLog = shootLog;
    }

    this.isDebugPlay = false;
  }

  public async startPlay(
    velocity: number,
    isResume = false,
    isPlayDebug = false,
  ) {
    this.pinataPartyState.isPlaying = true;
    this.pinataPartyController.pinataPartyAreaController.setInputEvent(false);
    this.resetHandAnimation();

    // if velocity is lower than minimum, bypass physics to have it bounce
    if (velocity < MINIMUM_VELOCITY) {
      const shootLog = await this.playBouncingBall(velocity);
      return shootLog;
    }
    if (!isResume) this.saveGameData(velocity);
    this.deductPearl();

    const initialTime = serverTimestampTicker.get();
    this.pinataPartyController.pinataPartyGameplayController.shootBall(
      velocity,
    );

    const { multiplier: goalMultiplier, sensorName: goalHit } =
      await this.pinataPartyController.pinataPartyGameplayController.waitForGoal();
    const finalTime = serverTimestampTicker.get();

    const elapsedTime = finalTime - initialTime;

    this.hitGoal(goalMultiplier);
    const score = this.scoreManager.getScore();

    const submitData = await this.getPinataPartySubmitData(
      elapsedTime,
      goalMultiplier,
    );

    // is background process, so normally don't await
    const p = this.pushPendingSubmit(submitData);
    clearGameData();

    const shootLog: PinataShootLog = {
      velocity,
      score,
      rubberHit: this.pinataPartyState.rubberHit,
      goalMultiplier,
      goalHit,
      submitData,
    };

    logData('PinataPartyScore', shootLog);

    this.pinataPartyController.pinataPartyGameplayController.showBall(false);

    const isGetReward = this.isGetReward() && !isPlayDebug;
    if (isGetReward) {
      let result = await p;
      // retry if failed
      if (!result) result = await this.submitAllPendingSubmit(true);

      // call pinata party battle pass api
      await this.loadingManager.runTask(true, async () => {
        return await this.services.battlePass.fetchInfo(
          EventScheduleType.PINATA_PARTY,
        );
      });

      if (result) {
        // Show Score Anims
        await this.playScoreGoalAnimation(result, goalMultiplier);

        // Update Points State
        this.pinataPartyState.currentPoint = result.current_point;
        this.pinataPartyState.targetPoint = result.target_point;
        this.pinataPartyState.currentPinata = result.current_pinata;
      }
    } else {
      /**
       * Doesn't get reward, but progress may increase
       **/

      const { score, targetPoint, currentPoint, currentPinata, currentPearl } =
        this.pinataPartyState;

      // Show score anims
      await this.playScoreGoalAnimation(
        {
          current_point: currentPoint + score,
          target_point: targetPoint,
          current_pearl: currentPearl,
          current_pinata: currentPinata,
          reward: [],
        },
        goalMultiplier,
      );

      // Update Points State
      this.pinataPartyState.currentPoint = currentPoint + score;
    }

    this.pinataPartyController.pinataPartyGameplayController.showBall(true);
    this.resetGameplay(true);

    return shootLog;
  }

  private getGrandPrizePopUpConfig(
    rewardItem: EventRewardItem,
  ): GrandPrizePopUpConfig | null {
    if (!rewardItem) return null;
    const colorConfig = colorConfigSelector(store.getState());

    const {
      basic_reward: basicRewards,
      ldc_reward: ldcRewards,
      offline_reward: offlineRewards,
    } = rewardItem;

    const showBasicRewardAnimation =
      !!basicRewards && !ldcRewards && !offlineRewards;

    const rewardConfig = getPinataGrandPrizeConfig(
      this.services,
      BASE_SIZE_REWARD_ASSET,
      {
        rewards: basicRewards,
        ldcRewardData: ldcRewards,
        offlineRewardData: offlineRewards,
      },
    );

    const chestBackgroundPosition =
      this.pinataPartyController.pinataPartyAreaController.getPinataChestPosition();

    const chestWorldPosition = {
      x: (chestBackgroundPosition?.x || 0) + CHEST_POSITION_OFFSET_X,
      y: (chestBackgroundPosition?.y || 0) + CHEST_POSITION_OFFSET_Y,
    };

    const titleText: CreateTextConfig | undefined = rewardConfig?.singleReward
      ?.description
      ? {
          text: rewardConfig?.singleReward?.description,
          fillColor: colorConfig.color_pp_reward_title_fill,
          strokeWidth: 5,
          strokeColor: colorConfig.color_pp_reward_title_outline,
          shadowColor: 'rgba(0, 0, 0, 1)',
          fontSize: 44,
          shadowOffset: { x: 3, y: -3 },
        }
      : undefined;

    return {
      popUpContainerConfig: getPopUpContainerConfig(this.services),
      rewardConfig,
      showBasicRewardAnimation,
      chestWorldPosition,
      titleText,
      giftPopUpType: GIFT_POPUP_TYPE.PINATA_PARTY,
      audioOpenReward: AUDIO_KEY.PINATA_PARTY_REWARD_DISPLAY_SOUND,
    };
  }

  private isGetReward() {
    const { score, targetPoint, currentPoint } = this.pinataPartyState;

    return currentPoint + score >= targetPoint;
  }

  private async playBouncingBall(velocity: number): Promise<PinataShootLog> {
    const height =
      ((velocity - VELOCITY_ADDITION) /
        (MINIMUM_VELOCITY - VELOCITY_ADDITION)) *
      MAX_BOUNCE_HEIGHT;
    await this.pinataPartyController.pinataPartyGameplayController.bounceBackBall(
      height,
    );
    this.resetGameplay(false);

    const shootLog: PinataShootLog = {
      velocity,
      score: 0,
      rubberHit: this.pinataPartyState.rubberHit,
      goalMultiplier: 0,
      goalHit: '',
      submitData: undefined,
    };

    return shootLog;
  }

  private async resetGameplay(shouldPlayAnimation: boolean) {
    this.checkMultiplier();
    this.pinataPartyController.pinataPartyGameplayController.resetBall();
    this.pinataPartyController.pinataPartyGameplayController.resetEntranceBlocker();
    this.scoreManager.resetState();
    this.pinataPartyController.pinataPartyAreaController.setInputEvent(true);
    this.pinataPartyController.pinataPartyWebWorkerController?.postMessage(
      WEBWORKER_COMMAND.RESET_CUSTOM,
    );

    // Do not play reset animation if bounce is used
    if (!shouldPlayAnimation) return;

    this.pinataPartyController.pinataPartyGameplayController.resetRubbers();
  }

  public hitRubber(rubberTag: number, worldPosition: Position) {
    const rubberHitState = this.scoreManager.hitRubber(
      rubberTag,
      worldPosition,
    );

    this.soundEffectController.play(rubberHitState.hitSound);

    if (rubberHitState.multiplyRubber) {
      this.pinataPartyController.pinataPartyGameplayController.refreshPoint();
      this.pinataPartyController.pinataPartyGameplayController.playRubberMultiplyAnim();
    }

    const score = this.pinataPartyState.score;

    this.pinataPartyController.pinataPartyGameplayController.setScoreText(
      score.toString(),
    );

    if (rubberHitState.rubberProgression !== undefined) {
      this.pinataPartyController.pinataPartyGameplayController.setRubberProgression(
        rubberHitState.rubberProgression,
      );
    }

    if (rubberHitState.scoreAnimation) {
      this.pinataPartyController.pinataPartyGameplayController.spawnFloatingScore(
        rubberHitState.scoreAnimation,
        rubberHitState.scoreWorldPosition,
      );
    }

    if (rubberHitState.closeAnimation) {
      this.pinataPartyController.pinataPartyGameplayController.playRubberCloseAnim();
    }
  }

  public hitGoal(multiplier: number) {
    if (!this.scoreManager.getIsPlaying()) return;
    this.scoreManager.hitGoalMultiplier(multiplier);
  }

  private getPinataPartyAdsButtonConfig(): PinataPartyAdsButtonConfig {
    const campaignConfig = this.services.campaign.getConfig();
    const amount = campaignConfig.number_177 ?? 0;
    return {
      adsInfo: this.services.ads.getData(ADS_SCENARIO.PINATA_PARTY_PEARL),
      amountText: `+${amount}`,
    };
  }

  public initAdsButton() {
    const config = this.getPinataPartyAdsButtonConfig();
    this.pinataPartyController.pinataPartyAdsButtonController.init(config);
  }

  public async refreshPearl() {
    const result = await this.loadingManager.runTask(true, async () => {
      return await this.services.pinataParty.fetchInfo();
    });

    if (!result.data) return;

    this.setPearlAmount(result.data.current_pearl);
  }

  private deductPearl() {
    this.setPearlAmount(
      this.pinataPartyState.currentPearl - this.pinataPartyState.multiplier,
    );
  }

  private setPearlAmount(amount: number) {
    this.pinataPartyState.currentPearl = Math.max(amount, 0);
    this.pinataPartyController.pinataPearlContainerController.refreshLabel();
  }

  public async playScoreGoalAnimation(
    submitResponse: PinataPartySubmitResponse,
    multiplierValue: number,
  ) {
    const config = getPinataScoreAnimationConfig(
      this.pinataPartyState.score,
      multiplierValue,
    );
    const { pinataPartyGameplayController, pinataPartyAreaController } =
      this.pinataPartyController;

    // Step 1: Inits and Resets
    pinataPartyGameplayController.initScoreAnimation(
      config.startScore.toString(),
      `x${config.multiplier}`,
    );
    // End Step 1

    // Step 2: Split Score and Multipler
    const scoreHalfWidth = pinataPartyGameplayController.getScoreHalfWidth();
    const multiplierHalfWidth =
      pinataPartyGameplayController.getMultiplierHalfWidth();

    if (!scoreHalfWidth || !multiplierHalfWidth) return;

    this.soundEffectController.play(AUDIO_KEY.PINATA_PARTY_SCORE_SPARK_SOUND);
    await pinataPartyGameplayController.splitScoreAndMultiplier(
      multiplierHalfWidth,
      -scoreHalfWidth,
    );
    // End Step 2

    // Step 3: Merge Score and Multiplier
    await pinataPartyGameplayController.mergeScoreAndMultiplier(
      scoreHalfWidth,
      config.startScore,
      config.finalScore,
    );
    // End Step 3

    // Step 4: Score fly to points display
    const pointPosition = pinataPartyAreaController.getPointsWorldPosition();
    if (pointPosition) {
      pinataPartyGameplayController.setGlowWorldPosition(pointPosition);
      await pinataPartyGameplayController.moveScoreToWorldPosition(
        pointPosition,
      );
      this.soundEffectController.play(AUDIO_KEY.PINATA_PARTY_SCORE_SPARK_SOUND);
    }
    // End Step 4

    // Step 5: Score Display Animation & Resets
    this.soundEffectController.play(
      AUDIO_KEY.PINATA_PARTY_PROGRESS_BAR_ADDED_SOUND,
    );
    pinataPartyGameplayController.showScoreDisplayAnimationAndResets();
    // End Step 5

    // Step 6: Play Progress Bar Animations and Pinata Reward Opening Sequences
    await this.playPointsProgressBarAnimation(submitResponse);
    // End Step 6
  }

  public async playPointsProgressBarAnimation(
    submitResponse: PinataPartySubmitResponse,
  ) {
    const config = getPinataPointsProgressBarConfig(
      this.services,
      this.pinataPartyState,
      submitResponse,
    );

    const { animationData, pointsTextSuffix } = config;

    for (let i = 0; i < animationData.length; i++) {
      let pinataFallPromise;
      let pointsProgressPromise;

      if (i > 0) {
        pinataFallPromise =
          this.pinataPartyController.pinataPartyAreaController.playPinataFall();
      }

      const currentAnimData = animationData[i];

      pointsProgressPromise = await AnimationHelper.TweenNumber(
        currentAnimData.startProgress,
        currentAnimData.endProgress,
        currentAnimData.duration,
        (num) => {
          this.pinataPartyController.pinataPartyAreaController.setPointsProgress(
            num,
            currentAnimData.maxProgress,
            pointsTextSuffix,
          );
        },
      );

      await Promise.all([pinataFallPromise, pointsProgressPromise]);

      if (currentAnimData.endProgress >= currentAnimData.maxProgress) {
        const popUpConfig = this.getGrandPrizePopUpConfig(
          submitResponse.reward[i],
        );

        if (popUpConfig) {
          const rewardPopUp =
            await this.popUpMasterController.showPinataPartyGrandPrizePopUp(
              popUpConfig,
            );

          await rewardPopUp?.waitForConfirm();
        }
      }
    }
  }

  private pushPendingSubmit(data: PinataPartySubmitData) {
    this.pinataPartyState.pendingSubmits.push(data);
    savePinataPartyPendingSubmits(this.pinataPartyState.pendingSubmits);

    return this.submitAllPendingSubmit(false);
  }

  private async getPinataPartySubmitData(
    elapsedTime: number,
    goalMultiplier: number,
  ): Promise<PinataPartySubmitData> {
    const score = this.scoreManager.getScore();
    const pearlsUsed = this.pinataPartyState.multiplier;
    const bounces = Math.min(this.pinataPartyState.rubberHit.length, 11);

    const rawData = `${pearlsUsed}-${elapsedTime}-${bounces}-${score}`;
    const enc = isOnMobileApp() ? await transformTextBE(rawData, 'pinata') : '';

    return {
      requestId: generateRequestId(),
      eventId: this.services.campaign.id,
      pinataPartyId: this.services.pinataParty.getPinataPartyId() ?? 0,
      pearlsUsed,
      score,
      elapsedTime,
      bounces,
      enc,
      goalMultiplier,
    };
  }

  /**
   * submit all pending submit in pinata state
   * @param showLoadingUi if true, show error popup, retry popup an overlay
   * @returns only return response of latest submit and only return if all submit is successfull
   */
  public async submitAllPendingSubmit(
    showLoadingUi: boolean,
  ): Promise<PinataPartySubmitResponse | null> {
    const pendingSubmits = this.pinataPartyState.pendingSubmits;
    return await submitPinataPartyGameplay(pendingSubmits, showLoadingUi).then(
      async ({ data, error }) => {
        if (error instanceof ServerError) {
          clearPinataPartyPendingSubmits();
          await this.loadingManager.showErrorPopUp(error);
          this.sceneManager.goToScene(SCENE_KEY.LANDING, {});
          return null;
        }

        // remove all resolved submit
        this.pinataPartyState.pendingSubmits =
          this.pinataPartyState.pendingSubmits.filter((submit) => {
            return !submit.response;
          });

        savePinataPartyPendingSubmits(this.pinataPartyState.pendingSubmits);

        if (error) return null;
        return data;
      },
    );
  }

  public async saveGameData(velocity: number) {
    const timestamp = serverTimestampTicker.get() + MAX_PLAY_TIME;
    const savedData: SavedData = {
      type: AUTOSAVE_TYPE.PINATA,
      version: APP_VERSION,
      userId: getUserId(),
      timestamp,
      velocity,
      multiplierIndex: this.pinataPartyState.multiplierIndex,
    };
    await saveGameData(savedData);
  }

  public updateIdleAnim(dt: number) {
    if (!this.pinataPartyState.isInGameplay) return;
    if (this.pinataPartyState.isPlaying) return; // no need to update during gameplay

    this.pinataPartyState.idleCountdown.deductDuration(dt);

    if (this.pinataPartyState.idleCountdown.isFinished()) {
      this.pinataPartyController.pinataPartyHandDragonBones.setActive(true);
      this.pinataPartyController.pinataPartyHandDragonBones.playAnimationOnce(
        'animtion0',
      );
      this.pinataPartyState.idleCountdown.reset();
    }
  }

  public playHandAnimation() {
    this.pinataPartyController.pinataPartyHandDragonBones.setActive(true);
    this.pinataPartyController.pinataPartyHandDragonBones.playAnimationOnce(
      'animtion0',
    );
  }

  public resetHandAnimation() {
    this.pinataPartyState.idleCountdown.reset();
    this.pinataPartyController.pinataPartyHandDragonBones.setActive(false);
  }

  public getGameplayTrackerConfig(): PinataPartyGameplayTrackerConfig {
    return {
      currentPinataChance: this.pinataPartyState.currentPearl,
      currentPinataMultiplier: this.pinataPartyState.multiplier,
      currentPinataPoint: this.pinataPartyState.currentPoint,
    };
  }
}
