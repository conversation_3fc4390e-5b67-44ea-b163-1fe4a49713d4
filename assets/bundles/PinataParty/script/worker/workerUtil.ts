import {
  <PERSON><PERSON><PERSON><PERSON>2D,
  Node,
  RigidBody2D,
  ERigidBody2DType,
  Collider2D,
  BoxCollider2D,
  PolygonCollider2D,
  Vec3,
} from 'cc';
import { BodyDefinition } from './workerInterface';
import { RAD_TO_DEG } from '../../../../script/constants';
import { IS_RUNTIME } from '../../../../script/util/runtime';
import { queryParamSelector } from '../../../../redux/selector/general';
import { store } from '../../../../redux/stores';

export function usePhysicsWorker(): boolean {
  const queryParam = queryParamSelector(store.getState());
  return IS_RUNTIME || queryParam.force_worker;
}

function getBodytype(type: ERigidBody2DType): BodyDefinition['bodyType'] {
  switch (type) {
    case ERigidBody2DType.Dynamic:
      return 'dynamic';

    case ERigidBody2DType.Kinematic: // not used
    case ERigidBody2DType.Animated: // used as static
    case ERigidBody2DType.Static:
      return 'static';

    default:
      const t: never = type;
      break;
  }
}

export function getBodyDefinition(
  node: Node,
  root?: Node,
): BodyDefinition | null {
  const rigidBody = node.getComponent(RigidBody2D);
  if (!rigidBody) return null;

  const collider = node.getComponent(Collider2D);
  if (!collider) return null;

  let position = node.getWorldPosition();
  let angle = node.getWorldRotation().getEulerAngles(new Vec3()).z / RAD_TO_DEG;
  if (root) {
    const rootPos = root.getWorldPosition();
    position.x -= rootPos.x;
    position.y -= rootPos.y;

    const parentAngle =
      root.getWorldRotation().getEulerAngles(new Vec3()).z / RAD_TO_DEG;
    angle -= -parentAngle;
  }

  const bodyDefinition: BodyDefinition = {
    id: node.uuid,
    position,
    angle,
    scale: 1,

    bodyType: getBodytype(rigidBody.type),
    linearVelocity: rigidBody.linearVelocity,
    linearDamping: rigidBody.linearDamping,
    angularVelocity: rigidBody.angularVelocity,
    angularDamping: rigidBody.angularDamping,
    listenCollision: rigidBody.enabledContactListener,

    density: collider.density,
    friction: collider.friction,
    restitution: collider.restitution,
    gravityScale: rigidBody.gravityScale,
    group: collider.group,
    isSensor: collider.sensor,
    tag: collider.tag,
  };

  if (collider instanceof CircleCollider2D) {
    bodyDefinition.shapeType = 'circle';
    // note : worker only have single scaling, can't make elipse with different x y scaling
    bodyDefinition.radius = collider.radius * node.scale.x;
  } else if (collider instanceof BoxCollider2D) {
    bodyDefinition.shapeType = 'box';
    bodyDefinition.boxSize = {
      width: collider.size.width * node.scale.x,
      height: collider.size.height * node.scale.y,
    };
  } else if (collider instanceof PolygonCollider2D) {
    bodyDefinition.shapeType = 'polygon';
    bodyDefinition.points = collider.points.map((point) =>
      point.clone().multiply2f(node.scale.x, node.scale.y),
    );
  }

  return bodyDefinition;
}
